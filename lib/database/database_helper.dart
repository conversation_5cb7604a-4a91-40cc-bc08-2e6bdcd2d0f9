import 'dart:async';
import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import '../models/models.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, 'indus_track.db');
    
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create equipments table
    await db.execute('''
      CREATE TABLE equipments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        model TEXT NOT NULL,
        serial_number TEXT NOT NULL UNIQUE,
        location TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'active',
        last_service_date INTEGER,
        purchase_date INTEGER NOT NULL,
        specifications TEXT,
        image_path TEXT,
        notes TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Create work_orders table
    await db.execute('''
      CREATE TABLE work_orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        equipment_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'open',
        priority TEXT NOT NULL DEFAULT 'medium',
        created_at INTEGER NOT NULL,
        scheduled_at INTEGER,
        completed_at INTEGER,
        assigned_to TEXT,
        completion_notes TEXT,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (equipment_id) REFERENCES equipments (id) ON DELETE CASCADE
      )
    ''');

    // Create checklist_items table
    await db.execute('''
      CREATE TABLE checklist_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        work_order_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        is_done INTEGER NOT NULL DEFAULT 0,
        sort_order INTEGER NOT NULL DEFAULT 0,
        completed_at INTEGER,
        completed_by TEXT,
        notes TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (work_order_id) REFERENCES work_orders (id) ON DELETE CASCADE
      )
    ''');

    // Create attachments table
    await db.execute('''
      CREATE TABLE attachments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        work_order_id INTEGER,
        equipment_id INTEGER,
        file_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        type TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        description TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (work_order_id) REFERENCES work_orders (id) ON DELETE CASCADE,
        FOREIGN KEY (equipment_id) REFERENCES equipments (id) ON DELETE CASCADE
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_equipment_status ON equipments (status)');
    await db.execute('CREATE INDEX idx_equipment_location ON equipments (location)');
    await db.execute('CREATE INDEX idx_work_order_status ON work_orders (status)');
    await db.execute('CREATE INDEX idx_work_order_equipment ON work_orders (equipment_id)');
    await db.execute('CREATE INDEX idx_checklist_work_order ON checklist_items (work_order_id)');
    await db.execute('CREATE INDEX idx_attachment_work_order ON attachments (work_order_id)');
    await db.execute('CREATE INDEX idx_attachment_equipment ON attachments (equipment_id)');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    // For now, we'll just recreate the database
    if (oldVersion < newVersion) {
      await db.execute('DROP TABLE IF EXISTS attachments');
      await db.execute('DROP TABLE IF EXISTS checklist_items');
      await db.execute('DROP TABLE IF EXISTS work_orders');
      await db.execute('DROP TABLE IF EXISTS equipments');
      await _onCreate(db, newVersion);
    }
  }

  // Equipment CRUD operations
  Future<int> insertEquipment(Equipment equipment) async {
    final db = await database;
    return await db.insert('equipments', equipment.toMap());
  }

  Future<List<Equipment>> getAllEquipments() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'equipments',
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => Equipment.fromMap(maps[i]));
  }

  Future<Equipment?> getEquipmentById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'equipments',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Equipment.fromMap(maps.first);
    }
    return null;
  }

  Future<List<Equipment>> searchEquipments(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'equipments',
      where: 'name LIKE ? OR model LIKE ? OR serial_number LIKE ? OR location LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%', '%$query%'],
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => Equipment.fromMap(maps[i]));
  }

  Future<List<Equipment>> getEquipmentsByStatus(EquipmentStatus status) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'equipments',
      where: 'status = ?',
      whereArgs: [status.name],
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => Equipment.fromMap(maps[i]));
  }

  Future<int> updateEquipment(Equipment equipment) async {
    final db = await database;
    return await db.update(
      'equipments',
      equipment.toMap(),
      where: 'id = ?',
      whereArgs: [equipment.id],
    );
  }

  Future<int> deleteEquipment(int id) async {
    final db = await database;
    return await db.delete(
      'equipments',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Work Order CRUD operations
  Future<int> insertWorkOrder(WorkOrder workOrder) async {
    final db = await database;
    return await db.insert('work_orders', workOrder.toMap());
  }

  Future<List<WorkOrder>> getAllWorkOrders() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'work_orders',
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => WorkOrder.fromMap(maps[i]));
  }

  Future<WorkOrder?> getWorkOrderById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'work_orders',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return WorkOrder.fromMap(maps.first);
    }
    return null;
  }

  Future<List<WorkOrder>> getWorkOrdersByEquipment(int equipmentId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'work_orders',
      where: 'equipment_id = ?',
      whereArgs: [equipmentId],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => WorkOrder.fromMap(maps[i]));
  }

  Future<List<WorkOrder>> getWorkOrdersByStatus(WorkOrderStatus status) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'work_orders',
      where: 'status = ?',
      whereArgs: [status.name],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => WorkOrder.fromMap(maps[i]));
  }

  Future<List<WorkOrder>> searchWorkOrders(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'work_orders',
      where: 'title LIKE ? OR description LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => WorkOrder.fromMap(maps[i]));
  }

  Future<int> updateWorkOrder(WorkOrder workOrder) async {
    final db = await database;
    return await db.update(
      'work_orders',
      workOrder.toMap(),
      where: 'id = ?',
      whereArgs: [workOrder.id],
    );
  }

  Future<int> deleteWorkOrder(int id) async {
    final db = await database;
    return await db.delete(
      'work_orders',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Checklist Item CRUD operations
  Future<int> insertChecklistItem(ChecklistItem item) async {
    final db = await database;
    return await db.insert('checklist_items', item.toMap());
  }

  Future<List<ChecklistItem>> getChecklistItemsByWorkOrder(int workOrderId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'checklist_items',
      where: 'work_order_id = ?',
      whereArgs: [workOrderId],
      orderBy: 'sort_order ASC, created_at ASC',
    );
    return List.generate(maps.length, (i) => ChecklistItem.fromMap(maps[i]));
  }

  Future<ChecklistItem?> getChecklistItemById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'checklist_items',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return ChecklistItem.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateChecklistItem(ChecklistItem item) async {
    final db = await database;
    return await db.update(
      'checklist_items',
      item.toMap(),
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }

  Future<int> deleteChecklistItem(int id) async {
    final db = await database;
    return await db.delete(
      'checklist_items',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> deleteChecklistItemsByWorkOrder(int workOrderId) async {
    final db = await database;
    await db.delete(
      'checklist_items',
      where: 'work_order_id = ?',
      whereArgs: [workOrderId],
    );
  }

  // Attachment CRUD operations
  Future<int> insertAttachment(Attachment attachment) async {
    final db = await database;
    return await db.insert('attachments', attachment.toMap());
  }

  Future<List<Attachment>> getAttachmentsByWorkOrder(int workOrderId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'attachments',
      where: 'work_order_id = ?',
      whereArgs: [workOrderId],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Attachment.fromMap(maps[i]));
  }

  Future<List<Attachment>> getAttachmentsByEquipment(int equipmentId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'attachments',
      where: 'equipment_id = ?',
      whereArgs: [equipmentId],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Attachment.fromMap(maps[i]));
  }

  Future<Attachment?> getAttachmentById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'attachments',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Attachment.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateAttachment(Attachment attachment) async {
    final db = await database;
    return await db.update(
      'attachments',
      attachment.toMap(),
      where: 'id = ?',
      whereArgs: [attachment.id],
    );
  }

  Future<int> deleteAttachment(int id) async {
    final db = await database;
    return await db.delete(
      'attachments',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> deleteAttachmentsByWorkOrder(int workOrderId) async {
    final db = await database;
    await db.delete(
      'attachments',
      where: 'work_order_id = ?',
      whereArgs: [workOrderId],
    );
  }

  Future<void> deleteAttachmentsByEquipment(int equipmentId) async {
    final db = await database;
    await db.delete(
      'attachments',
      where: 'equipment_id = ?',
      whereArgs: [equipmentId],
    );
  }

  // Statistics and Dashboard methods
  Future<Map<String, int>> getDashboardStats() async {
    final db = await database;

    final totalEquipment = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM equipments')
    ) ?? 0;

    final activeEquipment = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM equipments WHERE status = ?', ['active'])
    ) ?? 0;

    final openWorkOrders = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM work_orders WHERE status = ?', ['open'])
    ) ?? 0;

    final inProgressWorkOrders = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM work_orders WHERE status = ?', ['inProgress'])
    ) ?? 0;

    final completedWorkOrders = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM work_orders WHERE status = ?', ['completed'])
    ) ?? 0;

    final overdueWorkOrders = Sqflite.firstIntValue(
      await db.rawQuery(
        'SELECT COUNT(*) FROM work_orders WHERE status != ? AND scheduled_at < ?',
        ['completed', DateTime.now().millisecondsSinceEpoch]
      )
    ) ?? 0;

    return {
      'totalEquipment': totalEquipment,
      'activeEquipment': activeEquipment,
      'openWorkOrders': openWorkOrders,
      'inProgressWorkOrders': inProgressWorkOrders,
      'completedWorkOrders': completedWorkOrders,
      'overdueWorkOrders': overdueWorkOrders,
    };
  }

  Future<List<WorkOrder>> getUpcomingWorkOrders({int days = 7}) async {
    final db = await database;
    final now = DateTime.now();
    final future = now.add(Duration(days: days));

    final List<Map<String, dynamic>> maps = await db.query(
      'work_orders',
      where: 'status != ? AND scheduled_at BETWEEN ? AND ?',
      whereArgs: [
        'completed',
        now.millisecondsSinceEpoch,
        future.millisecondsSinceEpoch,
      ],
      orderBy: 'scheduled_at ASC',
    );
    return List.generate(maps.length, (i) => WorkOrder.fromMap(maps[i]));
  }

  Future<List<WorkOrder>> getOverdueWorkOrders() async {
    final db = await database;
    final now = DateTime.now().millisecondsSinceEpoch;

    final List<Map<String, dynamic>> maps = await db.query(
      'work_orders',
      where: 'status != ? AND scheduled_at < ?',
      whereArgs: ['completed', now],
      orderBy: 'scheduled_at ASC',
    );
    return List.generate(maps.length, (i) => WorkOrder.fromMap(maps[i]));
  }

  // Database utility methods
  Future<void> clearAllData() async {
    final db = await database;
    await db.delete('attachments');
    await db.delete('checklist_items');
    await db.delete('work_orders');
    await db.delete('equipments');
  }

  Future<void> closeDatabase() async {
    final db = await database;
    await db.close();
    _database = null;
  }
}
