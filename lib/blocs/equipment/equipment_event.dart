import 'package:equatable/equatable.dart';
import '../../models/models.dart';

abstract class EquipmentEvent extends Equatable {
  const EquipmentEvent();

  @override
  List<Object?> get props => [];
}

class LoadEquipments extends EquipmentEvent {
  const LoadEquipments();
}

class SearchEquipments extends EquipmentEvent {
  final String query;

  const SearchEquipments(this.query);

  @override
  List<Object> get props => [query];
}

class FilterEquipmentsByStatus extends EquipmentEvent {
  final EquipmentStatus? status;

  const FilterEquipmentsByStatus(this.status);

  @override
  List<Object?> get props => [status];
}

class AddEquipment extends EquipmentEvent {
  final Equipment equipment;

  const AddEquipment(this.equipment);

  @override
  List<Object> get props => [equipment];
}

class UpdateEquipment extends EquipmentEvent {
  final Equipment equipment;

  const UpdateEquipment(this.equipment);

  @override
  List<Object> get props => [equipment];
}

class DeleteEquipment extends EquipmentEvent {
  final int equipmentId;

  const DeleteEquipment(this.equipmentId);

  @override
  List<Object> get props => [equipmentId];
}

class LoadEquipmentDetails extends EquipmentEvent {
  final int equipmentId;

  const LoadEquipmentDetails(this.equipmentId);

  @override
  List<Object> get props => [equipmentId];
}

class RefreshEquipments extends EquipmentEvent {
  const RefreshEquipments();
}
