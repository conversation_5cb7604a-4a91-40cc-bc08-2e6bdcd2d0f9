import 'package:equatable/equatable.dart';
import '../../models/models.dart';

abstract class EquipmentState extends Equatable {
  const EquipmentState();

  @override
  List<Object?> get props => [];
}

class EquipmentInitial extends EquipmentState {
  const EquipmentInitial();
}

class EquipmentLoading extends EquipmentState {
  const EquipmentLoading();
}

class EquipmentLoaded extends EquipmentState {
  final List<Equipment> equipments;
  final String? searchQuery;
  final EquipmentStatus? statusFilter;

  const EquipmentLoaded({
    required this.equipments,
    this.searchQuery,
    this.statusFilter,
  });

  @override
  List<Object?> get props => [equipments, searchQuery, statusFilter];

  EquipmentLoaded copyWith({
    List<Equipment>? equipments,
    String? searchQuery,
    EquipmentStatus? statusFilter,
  }) {
    return EquipmentLoaded(
      equipments: equipments ?? this.equipments,
      searchQuery: searchQuery ?? this.searchQuery,
      statusFilter: statusFilter ?? this.statusFilter,
    );
  }
}

class EquipmentDetailsLoaded extends EquipmentState {
  final Equipment equipment;
  final List<WorkOrder> workOrders;
  final List<Attachment> attachments;

  const EquipmentDetailsLoaded({
    required this.equipment,
    required this.workOrders,
    required this.attachments,
  });

  @override
  List<Object> get props => [equipment, workOrders, attachments];
}

class EquipmentError extends EquipmentState {
  final String message;

  const EquipmentError(this.message);

  @override
  List<Object> get props => [message];
}

class EquipmentOperationSuccess extends EquipmentState {
  final String message;

  const EquipmentOperationSuccess(this.message);

  @override
  List<Object> get props => [message];
}
