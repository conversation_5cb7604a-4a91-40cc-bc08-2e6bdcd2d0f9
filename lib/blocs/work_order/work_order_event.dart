import 'package:equatable/equatable.dart';
import '../../models/models.dart';

abstract class WorkOrderEvent extends Equatable {
  const WorkOrderEvent();

  @override
  List<Object?> get props => [];
}

class LoadWorkOrders extends WorkOrderEvent {
  const LoadWorkOrders();
}

class SearchWorkOrders extends WorkOrderEvent {
  final String query;

  const SearchWorkOrders(this.query);

  @override
  List<Object> get props => [query];
}

class FilterWorkOrdersByStatus extends WorkOrderEvent {
  final WorkOrderStatus? status;

  const FilterWorkOrdersByStatus(this.status);

  @override
  List<Object?> get props => [status];
}

class FilterWorkOrdersByEquipment extends WorkOrderEvent {
  final int equipmentId;

  const FilterWorkOrdersByEquipment(this.equipmentId);

  @override
  List<Object> get props => [equipmentId];
}

class AddWorkOrder extends WorkOrderEvent {
  final WorkOrder workOrder;
  final List<String> checklistItems;

  const AddWorkOrder(this.workOrder, this.checklistItems);

  @override
  List<Object> get props => [workOrder, checklistItems];
}

class UpdateWorkOrder extends WorkOrderEvent {
  final WorkOrder workOrder;

  const UpdateWorkOrder(this.workOrder);

  @override
  List<Object> get props => [workOrder];
}

class DeleteWorkOrder extends WorkOrderEvent {
  final int workOrderId;

  const DeleteWorkOrder(this.workOrderId);

  @override
  List<Object> get props => [workOrderId];
}

class LoadWorkOrderDetails extends WorkOrderEvent {
  final int workOrderId;

  const LoadWorkOrderDetails(this.workOrderId);

  @override
  List<Object> get props => [workOrderId];
}

class UpdateChecklistItem extends WorkOrderEvent {
  final ChecklistItem checklistItem;

  const UpdateChecklistItem(this.checklistItem);

  @override
  List<Object> get props => [checklistItem];
}

class AddChecklistItem extends WorkOrderEvent {
  final int workOrderId;
  final String title;
  final String? description;

  const AddChecklistItem(this.workOrderId, this.title, this.description);

  @override
  List<Object?> get props => [workOrderId, title, description];
}

class DeleteChecklistItem extends WorkOrderEvent {
  final int checklistItemId;

  const DeleteChecklistItem(this.checklistItemId);

  @override
  List<Object> get props => [checklistItemId];
}

class CompleteWorkOrder extends WorkOrderEvent {
  final int workOrderId;
  final String? completionNotes;

  const CompleteWorkOrder(this.workOrderId, this.completionNotes);

  @override
  List<Object?> get props => [workOrderId, completionNotes];
}

class RefreshWorkOrders extends WorkOrderEvent {
  const RefreshWorkOrders();
}
