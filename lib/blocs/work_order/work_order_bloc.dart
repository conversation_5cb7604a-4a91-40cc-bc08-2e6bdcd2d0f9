import 'package:flutter_bloc/flutter_bloc.dart';
import '../../database/database_helper.dart';
import '../../models/models.dart';
import 'work_order_event.dart';
import 'work_order_state.dart';

class WorkOrderBloc extends Bloc<WorkOrderEvent, WorkOrderState> {
  final DatabaseHelper _databaseHelper;

  WorkOrderBloc({DatabaseHelper? databaseHelper})
      : _databaseHelper = databaseHelper ?? DatabaseHelper(),
        super(const WorkOrderInitial()) {
    on<LoadWorkOrders>(_onLoadWorkOrders);
    on<SearchWorkOrders>(_onSearchWorkOrders);
    on<FilterWorkOrdersByStatus>(_onFilterWorkOrdersByStatus);
    on<FilterWorkOrdersByEquipment>(_onFilterWorkOrdersByEquipment);
    on<AddWorkOrder>(_onAddWorkOrder);
    on<UpdateWorkOrder>(_onUpdateWorkOrder);
    on<DeleteWorkOrder>(_onDeleteWorkOrder);
    on<LoadWorkOrderDetails>(_onLoadWorkOrderDetails);
    on<UpdateChecklistItem>(_onUpdateChecklistItem);
    on<AddChecklistItem>(_onAddChecklistItem);
    on<DeleteChecklistItem>(_onDeleteChecklistItem);
    on<CompleteWorkOrder>(_onCompleteWorkOrder);
    on<RefreshWorkOrders>(_onRefreshWorkOrders);
  }

  Future<void> _onLoadWorkOrders(
    LoadWorkOrders event,
    Emitter<WorkOrderState> emit,
  ) async {
    try {
      emit(const WorkOrderLoading());
      final workOrders = await _databaseHelper.getAllWorkOrders();
      emit(WorkOrderLoaded(workOrders: workOrders));
    } catch (e) {
      emit(WorkOrderError('Failed to load work orders: ${e.toString()}'));
    }
  }

  Future<void> _onSearchWorkOrders(
    SearchWorkOrders event,
    Emitter<WorkOrderState> emit,
  ) async {
    try {
      emit(const WorkOrderLoading());
      final workOrders = event.query.isEmpty
          ? await _databaseHelper.getAllWorkOrders()
          : await _databaseHelper.searchWorkOrders(event.query);
      emit(WorkOrderLoaded(
        workOrders: workOrders,
        searchQuery: event.query.isEmpty ? null : event.query,
      ));
    } catch (e) {
      emit(WorkOrderError('Failed to search work orders: ${e.toString()}'));
    }
  }

  Future<void> _onFilterWorkOrdersByStatus(
    FilterWorkOrdersByStatus event,
    Emitter<WorkOrderState> emit,
  ) async {
    try {
      emit(const WorkOrderLoading());
      final workOrders = event.status == null
          ? await _databaseHelper.getAllWorkOrders()
          : await _databaseHelper.getWorkOrdersByStatus(event.status!);
      emit(WorkOrderLoaded(
        workOrders: workOrders,
        statusFilter: event.status,
      ));
    } catch (e) {
      emit(WorkOrderError('Failed to filter work orders: ${e.toString()}'));
    }
  }

  Future<void> _onFilterWorkOrdersByEquipment(
    FilterWorkOrdersByEquipment event,
    Emitter<WorkOrderState> emit,
  ) async {
    try {
      emit(const WorkOrderLoading());
      final workOrders = await _databaseHelper.getWorkOrdersByEquipment(event.equipmentId);
      emit(WorkOrderLoaded(
        workOrders: workOrders,
        equipmentFilter: event.equipmentId,
      ));
    } catch (e) {
      emit(WorkOrderError('Failed to filter work orders by equipment: ${e.toString()}'));
    }
  }

  Future<void> _onAddWorkOrder(
    AddWorkOrder event,
    Emitter<WorkOrderState> emit,
  ) async {
    try {
      final workOrderId = await _databaseHelper.insertWorkOrder(event.workOrder);
      
      // Add checklist items
      for (int i = 0; i < event.checklistItems.length; i++) {
        final checklistItem = ChecklistItem(
          workOrderId: workOrderId,
          title: event.checklistItems[i],
          sortOrder: i,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        await _databaseHelper.insertChecklistItem(checklistItem);
      }

      emit(const WorkOrderOperationSuccess('Work order created successfully'));
      add(const LoadWorkOrders());
    } catch (e) {
      emit(WorkOrderError('Failed to create work order: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateWorkOrder(
    UpdateWorkOrder event,
    Emitter<WorkOrderState> emit,
  ) async {
    try {
      await _databaseHelper.updateWorkOrder(event.workOrder);
      emit(const WorkOrderOperationSuccess('Work order updated successfully'));
      
      // Refresh details if currently viewing this work order
      if (state is WorkOrderDetailsLoaded) {
        final currentState = state as WorkOrderDetailsLoaded;
        if (currentState.workOrder.id == event.workOrder.id) {
          add(LoadWorkOrderDetails(event.workOrder.id!));
        }
      } else {
        add(const LoadWorkOrders());
      }
    } catch (e) {
      emit(WorkOrderError('Failed to update work order: ${e.toString()}'));
    }
  }

  Future<void> _onDeleteWorkOrder(
    DeleteWorkOrder event,
    Emitter<WorkOrderState> emit,
  ) async {
    try {
      await _databaseHelper.deleteWorkOrder(event.workOrderId);
      emit(const WorkOrderOperationSuccess('Work order deleted successfully'));
      add(const LoadWorkOrders());
    } catch (e) {
      emit(WorkOrderError('Failed to delete work order: ${e.toString()}'));
    }
  }

  Future<void> _onLoadWorkOrderDetails(
    LoadWorkOrderDetails event,
    Emitter<WorkOrderState> emit,
  ) async {
    try {
      emit(const WorkOrderLoading());
      
      final workOrder = await _databaseHelper.getWorkOrderById(event.workOrderId);
      if (workOrder == null) {
        emit(const WorkOrderError('Work order not found'));
        return;
      }

      final equipment = await _databaseHelper.getEquipmentById(workOrder.equipmentId);
      if (equipment == null) {
        emit(const WorkOrderError('Equipment not found'));
        return;
      }

      final checklistItems = await _databaseHelper.getChecklistItemsByWorkOrder(event.workOrderId);
      final attachments = await _databaseHelper.getAttachmentsByWorkOrder(event.workOrderId);

      emit(WorkOrderDetailsLoaded(
        workOrder: workOrder,
        equipment: equipment,
        checklistItems: checklistItems,
        attachments: attachments,
      ));
    } catch (e) {
      emit(WorkOrderError('Failed to load work order details: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateChecklistItem(
    UpdateChecklistItem event,
    Emitter<WorkOrderState> emit,
  ) async {
    try {
      await _databaseHelper.updateChecklistItem(event.checklistItem);
      
      // Refresh the current work order details if viewing
      if (state is WorkOrderDetailsLoaded) {
        final currentState = state as WorkOrderDetailsLoaded;
        if (currentState.checklistItems.any((item) => item.id == event.checklistItem.id)) {
          add(LoadWorkOrderDetails(event.checklistItem.workOrderId));
        }
      }
    } catch (e) {
      emit(WorkOrderError('Failed to update checklist item: ${e.toString()}'));
    }
  }

  Future<void> _onAddChecklistItem(
    AddChecklistItem event,
    Emitter<WorkOrderState> emit,
  ) async {
    try {
      final checklistItem = ChecklistItem(
        workOrderId: event.workOrderId,
        title: event.title,
        description: event.description,
        sortOrder: 999, // Add at the end
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      await _databaseHelper.insertChecklistItem(checklistItem);
      
      // Refresh the current work order details
      add(LoadWorkOrderDetails(event.workOrderId));
    } catch (e) {
      emit(WorkOrderError('Failed to add checklist item: ${e.toString()}'));
    }
  }

  Future<void> _onDeleteChecklistItem(
    DeleteChecklistItem event,
    Emitter<WorkOrderState> emit,
  ) async {
    try {
      final checklistItem = await _databaseHelper.getChecklistItemById(event.checklistItemId);
      if (checklistItem != null) {
        await _databaseHelper.deleteChecklistItem(event.checklistItemId);
        add(LoadWorkOrderDetails(checklistItem.workOrderId));
      }
    } catch (e) {
      emit(WorkOrderError('Failed to delete checklist item: ${e.toString()}'));
    }
  }

  Future<void> _onCompleteWorkOrder(
    CompleteWorkOrder event,
    Emitter<WorkOrderState> emit,
  ) async {
    try {
      final workOrder = await _databaseHelper.getWorkOrderById(event.workOrderId);
      if (workOrder != null) {
        final updatedWorkOrder = workOrder.copyWith(
          status: WorkOrderStatus.completed,
          completedAt: DateTime.now(),
          completionNotes: event.completionNotes,
          updatedAt: DateTime.now(),
        );
        
        await _databaseHelper.updateWorkOrder(updatedWorkOrder);
        emit(const WorkOrderOperationSuccess('Work order completed successfully'));
        add(LoadWorkOrderDetails(event.workOrderId));
      }
    } catch (e) {
      emit(WorkOrderError('Failed to complete work order: ${e.toString()}'));
    }
  }

  Future<void> _onRefreshWorkOrders(
    RefreshWorkOrders event,
    Emitter<WorkOrderState> emit,
  ) async {
    add(const LoadWorkOrders());
  }
}
