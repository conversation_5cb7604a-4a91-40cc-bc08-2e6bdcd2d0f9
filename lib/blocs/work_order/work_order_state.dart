import 'package:equatable/equatable.dart';
import '../../models/models.dart';

abstract class WorkOrderState extends Equatable {
  const WorkOrderState();

  @override
  List<Object?> get props => [];
}

class WorkOrderInitial extends WorkOrderState {
  const WorkOrderInitial();
}

class WorkOrderLoading extends WorkOrderState {
  const WorkOrderLoading();
}

class WorkOrderLoaded extends WorkOrderState {
  final List<WorkOrder> workOrders;
  final String? searchQuery;
  final WorkOrderStatus? statusFilter;
  final int? equipmentFilter;

  const WorkOrderLoaded({
    required this.workOrders,
    this.searchQuery,
    this.statusFilter,
    this.equipmentFilter,
  });

  @override
  List<Object?> get props => [workOrders, searchQuery, statusFilter, equipmentFilter];

  WorkOrderLoaded copyWith({
    List<WorkOrder>? workOrders,
    String? searchQuery,
    WorkOrderStatus? statusFilter,
    int? equipmentFilter,
  }) {
    return WorkOrderLoaded(
      workOrders: workOrders ?? this.workOrders,
      searchQuery: searchQuery ?? this.searchQuery,
      statusFilter: statusFilter ?? this.statusFilter,
      equipmentFilter: equipmentFilter ?? this.equipmentFilter,
    );
  }
}

class WorkOrderDetailsLoaded extends WorkOrderState {
  final WorkOrder workOrder;
  final Equipment equipment;
  final List<ChecklistItem> checklistItems;
  final List<Attachment> attachments;

  const WorkOrderDetailsLoaded({
    required this.workOrder,
    required this.equipment,
    required this.checklistItems,
    required this.attachments,
  });

  @override
  List<Object> get props => [workOrder, equipment, checklistItems, attachments];

  WorkOrderDetailsLoaded copyWith({
    WorkOrder? workOrder,
    Equipment? equipment,
    List<ChecklistItem>? checklistItems,
    List<Attachment>? attachments,
  }) {
    return WorkOrderDetailsLoaded(
      workOrder: workOrder ?? this.workOrder,
      equipment: equipment ?? this.equipment,
      checklistItems: checklistItems ?? this.checklistItems,
      attachments: attachments ?? this.attachments,
    );
  }
}

class WorkOrderError extends WorkOrderState {
  final String message;

  const WorkOrderError(this.message);

  @override
  List<Object> get props => [message];
}

class WorkOrderOperationSuccess extends WorkOrderState {
  final String message;

  const WorkOrderOperationSuccess(this.message);

  @override
  List<Object> get props => [message];
}
