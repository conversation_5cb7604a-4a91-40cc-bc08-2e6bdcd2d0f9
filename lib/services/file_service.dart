import 'dart:io';
import 'dart:typed_data';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/models.dart';
import '../database/database_helper.dart';

class FileService {
  static final FileService _instance = FileService._internal();
  factory FileService() => _instance;
  FileService._internal();

  final ImagePicker _picker = ImagePicker();
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get the app's documents directory for storing files
  Future<Directory> get _appDirectory async {
    final appDir = await getApplicationDocumentsDirectory();
    final indusTrackDir = Directory(path.join(appDir.path, 'indus_track'));
    if (!await indusTrackDir.exists()) {
      await indusTrackDir.create(recursive: true);
    }
    return indusTrackDir;
  }

  // Get subdirectories for different file types
  Future<Directory> get _imagesDirectory async {
    final appDir = await _appDirectory;
    final imagesDir = Directory(path.join(appDir.path, 'images'));
    if (!await imagesDir.exists()) {
      await imagesDir.create(recursive: true);
    }
    return imagesDir;
  }

  Future<Directory> get _videosDirectory async {
    final appDir = await _appDirectory;
    final videosDir = Directory(path.join(appDir.path, 'videos'));
    if (!await videosDir.exists()) {
      await videosDir.create(recursive: true);
    }
    return videosDir;
  }

  Future<Directory> get _documentsDirectory async {
    final appDir = await _appDirectory;
    final docsDir = Directory(path.join(appDir.path, 'documents'));
    if (!await docsDir.exists()) {
      await docsDir.create(recursive: true);
    }
    return docsDir;
  }

  // Request necessary permissions
  Future<bool> requestPermissions() async {
    final cameraStatus = await Permission.camera.request();
    final storageStatus = await Permission.storage.request();
    final photosStatus = await Permission.photos.request();
    
    return cameraStatus.isGranted && 
           (storageStatus.isGranted || photosStatus.isGranted);
  }

  // Take photo with camera
  Future<String?> takePhoto() async {
    try {
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        throw Exception('Camera permission not granted');
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        return await _saveImageFile(image);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to take photo: $e');
    }
  }

  // Pick image from gallery
  Future<String?> pickImageFromGallery() async {
    try {
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        throw Exception('Storage permission not granted');
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        return await _saveImageFile(image);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to pick image: $e');
    }
  }

  // Record video
  Future<String?> recordVideo() async {
    try {
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        throw Exception('Camera permission not granted');
      }

      final XFile? video = await _picker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(minutes: 5),
      );

      if (video != null) {
        return await _saveVideoFile(video);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to record video: $e');
    }
  }

  // Pick video from gallery
  Future<String?> pickVideoFromGallery() async {
    try {
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        throw Exception('Storage permission not granted');
      }

      final XFile? video = await _picker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 10),
      );

      if (video != null) {
        return await _saveVideoFile(video);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to pick video: $e');
    }
  }

  // Save image file to app directory
  Future<String> _saveImageFile(XFile image) async {
    final imagesDir = await _imagesDirectory;
    final fileName = '${DateTime.now().millisecondsSinceEpoch}_${path.basename(image.path)}';
    final savedPath = path.join(imagesDir.path, fileName);
    
    final imageBytes = await image.readAsBytes();
    final savedFile = File(savedPath);
    await savedFile.writeAsBytes(imageBytes);
    
    return savedPath;
  }

  // Save video file to app directory
  Future<String> _saveVideoFile(XFile video) async {
    final videosDir = await _videosDirectory;
    final fileName = '${DateTime.now().millisecondsSinceEpoch}_${path.basename(video.path)}';
    final savedPath = path.join(videosDir.path, fileName);
    
    final videoBytes = await video.readAsBytes();
    final savedFile = File(savedPath);
    await savedFile.writeAsBytes(videoBytes);
    
    return savedPath;
  }

  // Save attachment to database
  Future<int> saveAttachment({
    String? filePath,
    int? workOrderId,
    int? equipmentId,
    String? description,
  }) async {
    if (filePath == null) return -1;
    
    final file = File(filePath);
    if (!await file.exists()) {
      throw Exception('File does not exist');
    }

    final fileName = path.basename(filePath);
    final fileSize = await file.length();
    final fileExtension = path.extension(fileName).toLowerCase();
    
    AttachmentType type;
    if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].contains(fileExtension)) {
      type = AttachmentType.image;
    } else if (['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'].contains(fileExtension)) {
      type = AttachmentType.video;
    } else {
      type = AttachmentType.document;
    }

    final attachment = Attachment(
      workOrderId: workOrderId,
      equipmentId: equipmentId,
      fileName: fileName,
      filePath: filePath,
      type: type,
      fileSize: fileSize,
      description: description,
      createdAt: DateTime.now(),
    );

    return await _dbHelper.insertAttachment(attachment);
  }

  // Delete file from storage
  Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // Get file size in bytes
  Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }

  // Check if file exists
  Future<bool> fileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  // Get total storage used by the app
  Future<int> getTotalStorageUsed() async {
    try {
      final appDir = await _appDirectory;
      return await _getDirectorySize(appDir);
    } catch (e) {
      return 0;
    }
  }

  // Calculate directory size recursively
  Future<int> _getDirectorySize(Directory directory) async {
    int size = 0;
    try {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          size += await entity.length();
        }
      }
    } catch (e) {
      // Handle permission errors or other issues
    }
    return size;
  }

  // Clean up orphaned files (files not referenced in database)
  Future<void> cleanupOrphanedFiles() async {
    try {
      final attachments = await _dbHelper.database.then((db) => 
        db.query('attachments', columns: ['file_path']));
      
      final referencedPaths = attachments
          .map((a) => a['file_path'] as String)
          .toSet();

      final appDir = await _appDirectory;
      await for (final entity in appDir.list(recursive: true)) {
        if (entity is File && !referencedPaths.contains(entity.path)) {
          await entity.delete();
        }
      }
    } catch (e) {
      // Handle cleanup errors silently
    }
  }
}
