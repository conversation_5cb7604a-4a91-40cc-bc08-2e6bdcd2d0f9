import 'package:equatable/equatable.dart';

enum WorkOrderStatus {
  open,
  inProgress,
  completed,
  cancelled,
}

enum WorkOrderPriority {
  low,
  medium,
  high,
  urgent,
}

class WorkOrder extends Equatable {
  final int? id;
  final int equipmentId;
  final String title;
  final String description;
  final WorkOrderStatus status;
  final WorkOrderPriority priority;
  final DateTime createdAt;
  final DateTime? scheduledAt;
  final DateTime? completedAt;
  final String? assignedTo;
  final String? completionNotes;
  final DateTime updatedAt;

  const WorkOrder({
    this.id,
    required this.equipmentId,
    required this.title,
    required this.description,
    this.status = WorkOrderStatus.open,
    this.priority = WorkOrderPriority.medium,
    required this.createdAt,
    this.scheduledAt,
    this.completedAt,
    this.assignedTo,
    this.completionNotes,
    required this.updatedAt,
  });

  WorkOrder copyWith({
    int? id,
    int? equipmentId,
    String? title,
    String? description,
    WorkOrderStatus? status,
    WorkOrderPriority? priority,
    DateTime? createdAt,
    DateTime? scheduledAt,
    DateTime? completedAt,
    String? assignedTo,
    String? completionNotes,
    DateTime? updatedAt,
  }) {
    return WorkOrder(
      id: id ?? this.id,
      equipmentId: equipmentId ?? this.equipmentId,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      completedAt: completedAt ?? this.completedAt,
      assignedTo: assignedTo ?? this.assignedTo,
      completionNotes: completionNotes ?? this.completionNotes,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'equipment_id': equipmentId,
      'title': title,
      'description': description,
      'status': status.name,
      'priority': priority.name,
      'created_at': createdAt.millisecondsSinceEpoch,
      'scheduled_at': scheduledAt?.millisecondsSinceEpoch,
      'completed_at': completedAt?.millisecondsSinceEpoch,
      'assigned_to': assignedTo,
      'completion_notes': completionNotes,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory WorkOrder.fromMap(Map<String, dynamic> map) {
    return WorkOrder(
      id: map['id']?.toInt(),
      equipmentId: map['equipment_id']?.toInt() ?? 0,
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      status: WorkOrderStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => WorkOrderStatus.open,
      ),
      priority: WorkOrderPriority.values.firstWhere(
        (e) => e.name == map['priority'],
        orElse: () => WorkOrderPriority.medium,
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      scheduledAt: map['scheduled_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['scheduled_at'])
          : null,
      completedAt: map['completed_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['completed_at'])
          : null,
      assignedTo: map['assigned_to'],
      completionNotes: map['completion_notes'],
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  bool get isOverdue {
    if (scheduledAt == null || status == WorkOrderStatus.completed) {
      return false;
    }
    return DateTime.now().isAfter(scheduledAt!);
  }

  @override
  List<Object?> get props => [
        id,
        equipmentId,
        title,
        description,
        status,
        priority,
        createdAt,
        scheduledAt,
        completedAt,
        assignedTo,
        completionNotes,
        updatedAt,
      ];

  @override
  String toString() {
    return 'WorkOrder(id: $id, title: $title, status: $status, equipmentId: $equipmentId)';
  }
}
