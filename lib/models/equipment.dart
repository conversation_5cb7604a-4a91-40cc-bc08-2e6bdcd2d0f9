import 'package:equatable/equatable.dart';

enum EquipmentStatus {
  active,
  maintenance,
  broken,
  retired,
}

class Equipment extends Equatable {
  final int? id;
  final String name;
  final String model;
  final String serialNumber;
  final String location;
  final EquipmentStatus status;
  final DateTime? lastServiceDate;
  final DateTime purchaseDate;
  final Map<String, dynamic> specifications;
  final String? imagePath;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Equipment({
    this.id,
    required this.name,
    required this.model,
    required this.serialNumber,
    required this.location,
    this.status = EquipmentStatus.active,
    this.lastServiceDate,
    required this.purchaseDate,
    this.specifications = const {},
    this.imagePath,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  Equipment copyWith({
    int? id,
    String? name,
    String? model,
    String? serialNumber,
    String? location,
    EquipmentStatus? status,
    DateTime? lastServiceDate,
    DateTime? purchaseDate,
    Map<String, dynamic>? specifications,
    String? imagePath,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Equipment(
      id: id ?? this.id,
      name: name ?? this.name,
      model: model ?? this.model,
      serialNumber: serialNumber ?? this.serialNumber,
      location: location ?? this.location,
      status: status ?? this.status,
      lastServiceDate: lastServiceDate ?? this.lastServiceDate,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      specifications: specifications ?? this.specifications,
      imagePath: imagePath ?? this.imagePath,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'model': model,
      'serial_number': serialNumber,
      'location': location,
      'status': status.name,
      'last_service_date': lastServiceDate?.millisecondsSinceEpoch,
      'purchase_date': purchaseDate.millisecondsSinceEpoch,
      'specifications': specifications.isNotEmpty ? 
          specifications.entries.map((e) => '${e.key}:${e.value}').join('|') : null,
      'image_path': imagePath,
      'notes': notes,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory Equipment.fromMap(Map<String, dynamic> map) {
    Map<String, dynamic> specs = {};
    if (map['specifications'] != null && map['specifications'].isNotEmpty) {
      final specsList = map['specifications'].split('|');
      for (final spec in specsList) {
        final parts = spec.split(':');
        if (parts.length == 2) {
          specs[parts[0]] = parts[1];
        }
      }
    }

    return Equipment(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      model: map['model'] ?? '',
      serialNumber: map['serial_number'] ?? '',
      location: map['location'] ?? '',
      status: EquipmentStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => EquipmentStatus.active,
      ),
      lastServiceDate: map['last_service_date'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['last_service_date'])
          : null,
      purchaseDate: DateTime.fromMillisecondsSinceEpoch(map['purchase_date']),
      specifications: specs,
      imagePath: map['image_path'],
      notes: map['notes'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        model,
        serialNumber,
        location,
        status,
        lastServiceDate,
        purchaseDate,
        specifications,
        imagePath,
        notes,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'Equipment(id: $id, name: $name, model: $model, serialNumber: $serialNumber, status: $status)';
  }
}
