import 'package:equatable/equatable.dart';

class ChecklistItem extends Equatable {
  final int? id;
  final int workOrderId;
  final String title;
  final String? description;
  final bool isDone;
  final int sortOrder;
  final DateTime? completedAt;
  final String? completedBy;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ChecklistItem({
    this.id,
    required this.workOrderId,
    required this.title,
    this.description,
    this.isDone = false,
    this.sortOrder = 0,
    this.completedAt,
    this.completedBy,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  ChecklistItem copyWith({
    int? id,
    int? workOrderId,
    String? title,
    String? description,
    bool? isDone,
    int? sortOrder,
    DateTime? completedAt,
    String? completedBy,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ChecklistItem(
      id: id ?? this.id,
      workOrderId: workOrderId ?? this.workOrderId,
      title: title ?? this.title,
      description: description ?? this.description,
      isDone: isDone ?? this.isDone,
      sortOrder: sortOrder ?? this.sortOrder,
      completedAt: completedAt ?? this.completedAt,
      completedBy: completedBy ?? this.completedBy,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'work_order_id': workOrderId,
      'title': title,
      'description': description,
      'is_done': isDone ? 1 : 0,
      'sort_order': sortOrder,
      'completed_at': completedAt?.millisecondsSinceEpoch,
      'completed_by': completedBy,
      'notes': notes,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory ChecklistItem.fromMap(Map<String, dynamic> map) {
    return ChecklistItem(
      id: map['id']?.toInt(),
      workOrderId: map['work_order_id']?.toInt() ?? 0,
      title: map['title'] ?? '',
      description: map['description'],
      isDone: (map['is_done'] ?? 0) == 1,
      sortOrder: map['sort_order']?.toInt() ?? 0,
      completedAt: map['completed_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['completed_at'])
          : null,
      completedBy: map['completed_by'],
      notes: map['notes'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  @override
  List<Object?> get props => [
        id,
        workOrderId,
        title,
        description,
        isDone,
        sortOrder,
        completedAt,
        completedBy,
        notes,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'ChecklistItem(id: $id, title: $title, isDone: $isDone, workOrderId: $workOrderId)';
  }
}

// Predefined checklist templates
class ChecklistTemplate {
  static const List<String> maintenanceChecklist = [
    'Check oil levels',
    'Inspect filters',
    'Test electrical connections',
    'Measure voltage/current',
    'Clean equipment surfaces',
    'Check for unusual noises',
    'Inspect safety devices',
    'Update maintenance log',
  ];

  static const List<String> inspectionChecklist = [
    'Visual inspection for damage',
    'Check mounting and fasteners',
    'Verify operational parameters',
    'Test emergency stops',
    'Check calibration',
    'Document findings',
  ];

  static const List<String> repairChecklist = [
    'Identify problem',
    'Gather required tools/parts',
    'Follow safety procedures',
    'Perform repair',
    'Test functionality',
    'Update equipment records',
  ];

  static List<String> getTemplate(String type) {
    switch (type.toLowerCase()) {
      case 'maintenance':
        return maintenanceChecklist;
      case 'inspection':
        return inspectionChecklist;
      case 'repair':
        return repairChecklist;
      default:
        return maintenanceChecklist;
    }
  }
}
