import 'package:equatable/equatable.dart';
import 'dart:io';

enum AttachmentType {
  image,
  video,
  document,
}

class Attachment extends Equatable {
  final int? id;
  final int? workOrderId;
  final int? equipmentId;
  final String fileName;
  final String filePath;
  final AttachmentType type;
  final int fileSize;
  final String? description;
  final DateTime createdAt;

  const Attachment({
    this.id,
    this.workOrderId,
    this.equipmentId,
    required this.fileName,
    required this.filePath,
    required this.type,
    required this.fileSize,
    this.description,
    required this.createdAt,
  });

  Attachment copyWith({
    int? id,
    int? workOrderId,
    int? equipmentId,
    String? fileName,
    String? filePath,
    AttachmentType? type,
    int? fileSize,
    String? description,
    DateTime? createdAt,
  }) {
    return Attachment(
      id: id ?? this.id,
      workOrderId: workOrderId ?? this.workOrderId,
      equipmentId: equipmentId ?? this.equipmentId,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      type: type ?? this.type,
      fileSize: fileSize ?? this.fileSize,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'work_order_id': workOrderId,
      'equipment_id': equipmentId,
      'file_name': fileName,
      'file_path': filePath,
      'type': type.name,
      'file_size': fileSize,
      'description': description,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  factory Attachment.fromMap(Map<String, dynamic> map) {
    return Attachment(
      id: map['id']?.toInt(),
      workOrderId: map['work_order_id']?.toInt(),
      equipmentId: map['equipment_id']?.toInt(),
      fileName: map['file_name'] ?? '',
      filePath: map['file_path'] ?? '',
      type: AttachmentType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => AttachmentType.image,
      ),
      fileSize: map['file_size']?.toInt() ?? 0,
      description: map['description'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
    );
  }

  bool get fileExists {
    return File(filePath).existsSync();
  }

  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  String get fileExtension {
    return fileName.split('.').last.toLowerCase();
  }

  bool get isImage {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    return imageExtensions.contains(fileExtension);
  }

  bool get isVideo {
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
    return videoExtensions.contains(fileExtension);
  }

  @override
  List<Object?> get props => [
        id,
        workOrderId,
        equipmentId,
        fileName,
        filePath,
        type,
        fileSize,
        description,
        createdAt,
      ];

  @override
  String toString() {
    return 'Attachment(id: $id, fileName: $fileName, type: $type, workOrderId: $workOrderId, equipmentId: $equipmentId)';
  }
}
